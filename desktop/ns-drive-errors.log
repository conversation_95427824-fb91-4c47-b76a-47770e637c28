[ERROR] 2025/07/04 21:57:06 handlers.go:150: TraceID: b295be23-bb9c-4279-8b89-21e8c1b6992a | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:57:06 handlers.go:154: TraceID: b295be23-bb9c-4279-8b89-21e8c1b6992a | Details: open : no such file or directory
[ERROR] 2025/07/04 21:57:06 handlers.go:150: TraceID: bb1a103d-f68c-4709-831e-b8f2b522cae3 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:57:06 handlers.go:154: TraceID: bb1a103d-f68c-4709-831e-b8f2b522cae3 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:57:06 handlers.go:159: TraceID: b295be23-bb9c-4279-8b89-21e8c1b6992a | Stack Trace:
goroutine 388 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x140004196f0, 0x140002a0a80, {0x1400028a880?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x140004196f0, {0x1012e2cc8?, 0x140003e3ef0?}, {0x1400028a880, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140000f0490?, {0x1012e2cc8?, 0x140003e3ef0?}, {0x1400028a880?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140000f0480)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x140000f0480)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x101294cc0?, 0x140000f0480?, 0x14000559c78?}, {0x100c64282, 0x4}, {0x101cbf480, 0x0, 0x10086f844?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x101294cc0?, 0x140000f0480?, 0x0?}, {0x101cbf480?, 0x0?, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x1400045b0e0, {0x1012f36b0, 0x140003cc570}, {0x101cbf480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 387
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:57:06 handlers.go:164: TraceID: b295be23-bb9c-4279-8b89-21e8c1b6992a | Underlying Error: open : no such file or directory
[ERROR] 2025/07/04 21:57:06 handlers.go:159: TraceID: bb1a103d-f68c-4709-831e-b8f2b522cae3 | Stack Trace:
goroutine 357 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x140004196f0, 0x140001a2850, {0x1400038a320?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x140004196f0, {0x1012e2cc8?, 0x140003f47b0?}, {0x1400038a320, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140000f0490?, {0x1012e2cc8?, 0x140003f47b0?}, {0x1400038a320?, 0x14000130460?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140000f0480)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetRemotes(0x140000f0480)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:185 +0x78
reflect.Value.call({0x101294cc0?, 0x140000f0480?, 0x140002d7c78?}, {0x100c64282, 0x4}, {0x101cbf480, 0x0, 0x10086f844?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x101294cc0?, 0x140000f0480?, 0x0?}, {0x101cbf480?, 0x0?, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x1400045b2c0, {0x1012f36b0, 0x140003e8720}, {0x101cbf480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 356
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:57:06 handlers.go:164: TraceID: bb1a103d-f68c-4709-831e-b8f2b522cae3 | Underlying Error: open .config/.profiles: no such file or directory
